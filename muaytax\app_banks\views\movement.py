import hashlib
import numpy as np
import pandas as pd
import pylightxl
import string
import json
import requests
from django.db.models import F, Sum

from datetime import datetime
from django.utils import timezone
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse
from django.shortcuts import get_object_or_404, redirect
from django.http import HttpResponseBadRequest, HttpResponseRedirect
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.core.serializers import serialize
from django.views.generic import CreateView, DeleteView, ListView, UpdateView
from django.conf import settings

from muaytax.dictionaries.models.bank_movement_template import BankMovementTemplate
from muaytax.dictionaries.models.currencies import Currency
from muaytax.dictionaries.models.importer_bankmovement_status import BankMovementImporterStatus
from muaytax.dictionaries.models.movement_status import MovementStatus
from muaytax.users.permissions import IsSellerShortnamePermission
from muaytax.app_banks.forms.movement import MovementCreateForm
from muaytax.app_banks.models.movement import Movement
from muaytax.app_importers.forms.bank_movements import BankMovementsChangeForm
from muaytax.app_importers.models.bank_movements import BankMovementsImporter
from muaytax.app_sellers.models.seller import Seller
from muaytax.app_banks.models.reconciliation import Reconciliation
from muaytax.app_banks.models.bank_rule import BankRule
from muaytax.dictionaries.models.reconciliation_type import ReconciliationType

from muaytax.app_banks.models.bank import Bank
from muaytax.app_banks.views.reconciliation import update_movement_status

# from muaytax.utils.api_currency import CurrencyConverter, convert_currency_on_invoices

class MovementsUploadView(LoginRequiredMixin, IsSellerShortnamePermission, ListView):
    model = BankMovementsImporter
    # form_class = BankMovementsChangeForm
    template_name_suffix = "_upload"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_queryset(self):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        bank = Bank.objects.filter(bank_seller = seller, id = self.kwargs["pk"]).first()
        bankmovements = BankMovementsImporter.objects.filter(bank = bank)
        return bankmovements

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        context["seller"] = seller

        bank = Bank.objects.filter(bank_seller = seller, id = self.kwargs["pk"]).first()
        context["bank"] = bank

        error_message = self.request.GET.get('error')
        if error_message:
            context['error'] = error_message


        context["json"] = {
            "seller": serialize("json", [context["seller"]]),
            "bank": serialize("json", [context["bank"]]),
        }

        return context

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class MovementsCreateView(LoginRequiredMixin, IsSellerShortnamePermission, CreateView):
    model = BankMovementsImporter
    form_class = BankMovementsChangeForm
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_form_kwargs(self):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        bank = Bank.objects.filter(bank_seller = seller, id = self.kwargs["pk"]).first()
        pendingstatus = BankMovementImporterStatus.objects.filter(code = "pending").first()
        kwargs = super().get_form_kwargs()
        kwargs["data"] = kwargs["data"].copy()
        kwargs["data"]["seller"] = seller
        kwargs["data"]["bank"] = bank
        kwargs["data"]["status"] = pendingstatus
        kwargs["data"]["error_message"] = "Pendiente de procesar"
        return kwargs

    # def form_valid(self, form):
    #     self.object = form.save()

    #     # if True:
    #     try:
    #         resp = self.process_movement_excel(form)
    #         error = resp['error']
    #         msg = resp['message']
    #         template = resp['banktemplate']

    #         if (error  == False):
    #             # Set 'status' to 'processed' and 'error_message'
    #             processed_status = BankMovementImporterStatus.objects.filter(code = "processed").first()
    #             self.object.status = processed_status
    #             self.object.error_message = msg if msg != None and len(msg) > 0 else "Procesado Correctamente"
    #             self.object.template = template
    #             redirect = self.get_success_url()
    #         else:
    #             # Set 'status' to 'error' and 'error_message'
    #             error_status = BankMovementImporterStatus.objects.filter(code = "error").first()
    #             self.object.status = error_status
    #             self.object.error_message = msg if msg != None and len(msg) > 0 else "Error procesando el archivo"
    #             self.object.template = template
    #             redirect = self.get_unsuccess_url()
    #     except Exception as e:
    #         print(f"Error procesando el archivo: {e}")
    #         # Set 'status' to 'error' and 'error_message'
    #         self.object.status = BankMovementImporterStatus.objects.filter(code = "error").first()
    #         self.object.error_message = "Error procesando el archivo"
    #         if ( "time data 'nan'" in str(e) ):
    #             self.object.error_message = "Error procesando el archivo: Formato de fecha invalido."
    #         elif ( "time" in str(e) and "does not match format" in str(e) ):
    #             self.object.error_message = "Error procesando el archivo: Formato de fecha invalido."
    #         redirect = self.get_unsuccess_url()

    #     # Save Oject
    #     self.object.save()

    #     # Redirect to success or unsuccess url
    #     return HttpResponseRedirect(redirect)

    def form_valid(self, form):
        self.object = form.save()

        try:
            resp = self.process_movement_excel(form)
            error = resp['error']
            msg = resp['message']
            template = resp['banktemplate']
            # Get count_new_mv from the response
            count_new_mv = resp.get('count_new_mv', 0)

            print(f"DEBUG - form_valid: count_new_mv={count_new_mv}")

            if (error == False):
                # Set 'status' to 'processed' and 'error_message'
                processed_status = BankMovementImporterStatus.objects.filter(code="processed").first()
                self.object.status = processed_status
                self.object.error_message = msg if msg != None and len(msg) > 0 else "Procesado Correctamente"
                self.object.template = template

                # Auto-apply bank rules for this bank after successful import
                if count_new_mv > 0:  # Only if new movements were created
                    print(f"DEBUG - form_valid: Applying rules for {count_new_mv} new movements")
                    self.rule_result = self.apply_bank_rules_to_imported_movements()
                    print(f"DEBUG - form_valid: Rule application result: {self.rule_result}")
                else:
                    print(f"DEBUG - form_valid: No new movements to apply rules to")

                redirect = self.get_success_url()
            else:
                # Set 'status' to 'error' and 'error_message'
                error_status = BankMovementImporterStatus.objects.filter(code="error").first()
                self.object.status = error_status
                self.object.error_message = msg if msg != None and len(msg) > 0 else "Error procesando el archivo"
                self.object.template = template
                redirect = self.get_unsuccess_url()
        except Exception as e:
            print(f"Error procesando el archivo: {e}")
            # Set 'status' to 'error' and 'error_message'
            self.object.status = BankMovementImporterStatus.objects.filter(code="error").first()
            self.object.error_message = "Error procesando el archivo"
            if ("time data 'nan'" in str(e)):
                self.object.error_message = "Error procesando el archivo: Formato de fecha invalido."
            elif ("time" in str(e) and "does not match format" in str(e)):
                self.object.error_message = "Error procesando el archivo: Formato de fecha invalido."
            redirect = self.get_unsuccess_url()

        # Save Object
        self.object.save()

        # Redirect to success or unsuccess url
        return HttpResponseRedirect(redirect)

    def apply_bank_rules_to_imported_movements(self):
        """Apply bank rules to newly imported movements."""
        try:
            seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
            bank = Bank.objects.filter(bank_seller=seller, id=self.kwargs["pk"]).first()

            print(f"DEBUG - apply_bank_rules: Seller={seller.shortname}, Bank ID={bank.id if bank else None}")

            if not bank:
                print(f"Error: Bank with ID {self.kwargs['pk']} not found")
                return None

            # Get rules for this bank, ordered by priority (highest first)
            rules = BankRule.objects.filter(
                bank=bank,
                seller=seller,
                is_active=True
            ).order_by('-priority')

            print(f"DEBUG - apply_bank_rules: Found {rules.count()} rules for bank ID {bank.id}")

            if not rules.exists():
                print(f"No rules found for bank ID {bank.id}")
                return None

            # Get pending and partially conciliated movements for this bank
            movements = Movement.objects.filter(
                bank=bank,
                status_id__in=['pending', 'partially-conciliated']
            )

            total_movements = movements.count()
            print(f"DEBUG - apply_bank_rules: Found {total_movements} movements to process")

            matched_movements = 0

            # Process each movement
            for movement in movements:
                # Skip if movement is already fully conciliated
                if movement.status_id == 'conciliated':
                    continue

                print(f"DEBUG - apply_bank_rules: Processing movement ID={movement.id}, concept={movement.concept}")

                # Process through rules in priority order
                for rule in rules:
                    # Skip rules without an accounting account
                    if not rule.accounting_account:
                        continue

                    # Get movement concept
                    concept = movement.concept or ""

                    # Case-insensitive comparison
                    concept_lower = concept.lower()
                    pattern_lower = rule.pattern.lower()

                    # Apply rule based on match type
                    match = False
                    match_type = rule.rule_match_type
                    if match_type == 'equals':
                        match = concept_lower == pattern_lower
                    elif match_type == 'starts_with':
                        match = concept_lower.startswith(pattern_lower)
                    elif match_type == 'ends_with':
                        match = concept_lower.endswith(pattern_lower)
                    elif match_type == 'contains':
                        match = pattern_lower in concept_lower

                    print(f"DEBUG - apply_bank_rules: Rule ID={rule.id}, pattern={rule.pattern}, match_type={match_type}, match={match}")

                    # If match found, create reconciliation
                    if match:
                        # Skip if movement is already fully conciliated
                        if movement.status_id == 'conciliated':
                            break

                        print(f"DEBUG - apply_bank_rules: Match found! Creating reconciliation")

                        # Get reconciliation type
                        rec_type = ReconciliationType.objects.filter(pk='account').first()
                        if not rec_type:
                            continue

                        # Calculate pending amount
                        reconciliations_amount = Reconciliation.objects.filter(movement=movement).aggregate(total=Sum(F('amount')))['total'] or 0

                        # Check for existing reconciliation with same account
                        existing_reconciliation = Reconciliation.objects.filter(
                            movement=movement,
                            accounting_account=rule.accounting_account
                        ).first()

                        # Exclude existing reconciliation from calculation if it exists
                        if existing_reconciliation:
                            reconciliations_amount -= existing_reconciliation.amount

                        # Calculate pending amount based on movement type
                        if movement.amount_euros < 0:
                            if reconciliations_amount < 0:
                                pending_amount = float(movement.amount_euros) - float(reconciliations_amount)
                            else:
                                pending_amount = float(movement.amount_euros) + float(reconciliations_amount)
                        else:
                            if reconciliations_amount < 0:
                                pending_amount = float(movement.amount_euros) + float(reconciliations_amount)
                            else:
                                pending_amount = float(movement.amount_euros) - float(reconciliations_amount)

                        # Round to 2 decimals
                        pending_amount = round(pending_amount, 2)

                        # For account reconciliation, use opposite sign
                        amount = -pending_amount

                        try:
                            # If existing reconciliation exists, delete it
                            if existing_reconciliation:
                                existing_reconciliation.delete()

                                # Recalculate pending amount
                                reconciliations_amount = Reconciliation.objects.filter(movement=movement).aggregate(total=Sum(F('amount')))['total'] or 0

                                if movement.amount_euros < 0:
                                    if reconciliations_amount < 0:
                                        pending_amount = float(movement.amount_euros) - float(reconciliations_amount)
                                    else:
                                        pending_amount = float(movement.amount_euros) + float(reconciliations_amount)
                                else:
                                    if reconciliations_amount < 0:
                                        pending_amount = float(movement.amount_euros) + float(reconciliations_amount)
                                    else:
                                        pending_amount = float(movement.amount_euros) - float(reconciliations_amount)

                                pending_amount = round(pending_amount, 2)
                                amount = -pending_amount

                            # Create new reconciliation
                            reconciliation = Reconciliation(
                                movement=movement,
                                accounting_account=rule.accounting_account,
                                accounting_account_detail=rule.accounting_account.code,
                                amount=amount,
                                type=rec_type,
                                invoice=None,
                                bank=None,
                                movement_transfer=None,
                                used_in_entry=False,
                                created_at=timezone.now(),
                                modified_at=timezone.now()
                            )
                            reconciliation.save()
                            matched_movements += 1

                            # Update movement status
                            try:
                                update_movement_status(seller, movement.pk)
                            except Exception as e:
                                print(f"Error updating movement status: {e}")
                        except Exception as e:
                            print(f"Error creating reconciliation for movement {movement.pk}: {e}")

                        # We've matched this movement, move to the next
                        break

            print(f"Auto-applied rules to {matched_movements} of {total_movements} movements")
            return {
                'total_movements': total_movements,
                'matched_movements': matched_movements
            }
        except Exception as e:
            print(f"Error applying bank rules automatically: {e}")
            import traceback
            traceback.print_exc()  # This will print the full traceback
            return None



    def form_invalid(self, form):
        shortname=self.kwargs["shortname"]
        bankid=self.kwargs["pk"]
        error_message = form.errors
        if 'file' in form.errors:
            error_message = form.errors['file']
        return redirect(reverse('app_banks:bank_movements_upload', args=[shortname, bankid]) + f'?toast_message={error_message}&toast_type=error')

    # def get_success_url(self) -> str:
    #     shortname=self.kwargs["shortname"]
    #     base_url = reverse('app_banks:bank_list', args=[shortname])
    #     # Add success message as query parameter
    #     if hasattr(self, 'object') and self.object:
    #         # Only show success message if processing was successful
    #         success_message = "Se procesó el archivo correctamente."
    #         return f"{base_url}?toast_message={success_message}&toast_type=success"
    #     return base_url

    def get_success_url(self) -> str:
        shortname=self.kwargs["shortname"]
        base_url = reverse('app_banks:bank_list', args=[shortname])
        # Add success message as query parameter
        if hasattr(self, 'object') and self.object:
            # Only show success message if processing was successful
            success_message = "Se procesó el archivo correctamente."

            # Add rule application info if available
            if hasattr(self, 'rule_result') and self.rule_result:
                total = self.rule_result.get('total_movements', 0)
                matched = self.rule_result.get('matched_movements', 0)
                if matched > 0:
                    success_message += f" Se aplicaron reglas a {matched} de {total} movimientos importados."

            return f"{base_url}?toast_message={success_message}&toast_type=success"
        return base_url

    def get_unsuccess_url(self) -> str:
        shortname = shortname=self.kwargs["shortname"]
        bank_id = self.kwargs["pk"]
        base_url = reverse(
            "app_banks:bank_movements_upload",
            args=[shortname, bank_id],
        )
        # Add error message as query parameter if it exists
        if hasattr(self, 'object') and self.object and self.object.error_message:
            return f"{base_url}?toast_message={self.object.error_message}&toast_type=error"
        return base_url

    def handle_no_permission(self):
            return HttpResponseRedirect(reverse("home"))

    def convertCurrencyToEur(self, currency, amount, date):
        try:
            seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        except:
            seller = None
        if seller:
            if seller.api_usage is None or seller.api_usage == "":
                api_usage = {}
            else:
                api_usage = json.loads(seller.api_usage)
            api="currency-api"
            if api in api_usage:
                api_usage[str(api)]=api_usage[str(api)]+1
                print("ya se encuentra, se actualiza, valor: " + str(api_usage[str(api)]) )
            else:
                api_usage[str(api)]=1
                print("no se encuentra, lo creamos")
            seller.api_usage = json.dumps(api_usage)
            seller.save()

        # # Reutilizar el CurrencyConverter desde el utils
        # converter = CurrencyConverter()
        # taxConversion = converter.convertCurrencyToEuro(base_currency=currency, amount=amount, date=date)

        # # Asegurarse de que el valor se trunca a 2 decimales
        # result = float(int(taxConversion * 100) / 100)

        baseUrl = 'https://api.currencyapi.com/v3/'
        apikey = settings.CURRENCY_API_KEY
        taxConversion = 0
        if  currency is not None and currency == 'EUR':
            taxConversion = 1
        elif date  is not None and currency is not None :

            try:
                url = baseUrl
                currentDate = datetime.now()
                invoiceDate = datetime.strptime(date + 'T23:59:59', '%Y-%m-%dT%H:%M:%S')

                if invoiceDate < currentDate:
                    url += 'historical'
                    url += '?currencies=EUR'
                    url += '&date=' + str(date)
                else:
                    url += 'latest'
                    url += '?currencies=EUR'
                url += '&base_currency=' + currency
                url += '&apikey=' + apikey

                r = requests.get(url)
                if r is not None and r.status_code == 200:
                    data = r.json()['data']
                    print(data)
                    taxConversion = float(data['EUR']['value'])
                print('taxConversion:'+str(taxConversion))
            except Exception as error:
                print('Error on getTaxConversion: ', repr(error))

        amount = float(str(amount))
        result = float(taxConversion * amount)
        result = float( int(result*100) / 100 ) # TRUNCATE TO 2 DECIMALS
        return result

    def excel_col_number(self, letra):
        """
        Convierte una letra de A a Z en su equivalente numérico (A=0, B=1, ..., Z=25).
        """
        r = None
        if (letra != None and letra.strip() != "" and letra.isalpha()):
            letras = string.ascii_uppercase
            r = letras.index(letra.upper())
        return r

    def excel_row_number(self, fila):
        """
        Resta 1 a la fila, asumiendo que las filas comienzan desde 1.
        """
        r = None
        if (fila != None and fila.strip() != "" and fila.isnumeric() and int(fila) > 0):
            r = int(fila) - 1
        return r

    def process_movement_excel(self, form):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        bank = Bank.objects.filter(bank_seller = seller, id = self.kwargs["pk"]).first()
        mv_status_pending = MovementStatus.objects.filter(code = "pending").first()

        error = None
        errormsg= None
        banktemplate = None
        count_new_mv = 0

        if form.is_valid():
            excel_file = self.request.FILES["file"]

            # Read Excel File
            df = None
            if excel_file.name.endswith('.xls'):
                # Reiniciar el puntero del archivo al principio
                excel_file.seek(0)
                # Leer el archivo Excel con Pandas
                df = pd.read_excel(excel_file, engine="xlrd", header=None)
            elif excel_file.name.endswith('.xlsx'):
                try:
                    # Reiniciar el puntero del archivo al principio
                    excel_file.seek(0)
                    # Leer el archivo Excel con Pandas
                    df = pd.read_excel(excel_file, engine="openpyxl", header=None)
                except:
                    # Reiniciar el puntero del archivo al principio
                    excel_file.seek(0)
                    # Leer el archivo Excel con pylightxl
                    db = pylightxl.readxl(excel_file)
                    # data sheet
                    name_first_sheet = db.ws_names[0]
                    sheet_data = list(db.ws(ws=name_first_sheet).rows)
                    # create dataframe
                    df = pd.DataFrame(data=sheet_data[0:], columns=sheet_data[0])

            # Get BankTemplates and Find the one that matches the file
            templates = BankMovementTemplate.objects.all()
            if excel_file.name.endswith('.csv'):
                templates = templates.exclude(csv_separator=None).exclude(csv_encoding=None)

            print("before templates")
            for template in templates:
                try:
                    print(f"Bank Template Name: {template.bank_name} | encoding: {template.csv_encoding} | separator: {template.csv_separator}")

                    # Read CSV File (IF ITS CSV)
                    if excel_file.name.endswith('.csv'):

                        from io import StringIO

                        # Reiniciar el puntero del archivo al principio
                        excel_file.seek(0)

                        # Delimiter / Separator
                        delimiter = ';'
                        if (template.csv_separator != None and template.csv_separator.strip() != ""):
                            delimiter = template.csv_separator

                        # Enconding
                        encoding= 'utf8'
                        if (template.csv_encoding != None and template.csv_encoding.strip() != ""):
                            encoding = template.csv_encoding

                        # Leer el contenido del archivo como una cadena utilizando la codificación
                        file_contents = excel_file.read().decode(encoding)

                        # Crear un objeto StringIO a partir de la cadena
                        excel_file_io = StringIO(file_contents)

                        # Read CSV File With Pandas
                        df = pd.read_csv(excel_file_io, encoding=encoding, delimiter=delimiter, lineterminator='\n', header=None)


                    found_bank_name = False
                    found_date = False
                    found_amount = False
                    found_concept = False
                    found_observation = False

                    # Get Bank Name
                    row = self.excel_row_number(template.bank_name_row)
                    col = self.excel_col_number(template.bank_name_col)
                    print("Bank Name Row: " + str(row) + " Col: " + str(col))
                    if (row != None and col != None):
                        bank_name = df.iloc[row, col]
                        if (bank_name != None and bank_name != np.nan and str(bank_name).strip().upper() == template.bank_name.strip().upper()):
                            found_bank_name = True
                    else:
                        bank_name = None
                        found_bank_name = None

                    # Get Date Header
                    row = self.excel_row_number(template.header_row)
                    start_row = self.excel_row_number(template.start_row)
                    col = self.excel_col_number(template.date_col)
                    if (row is not None and col is not None):
                        date_header = df.iloc[row, col]
                        if (date_header is not None and date_header is not np.nan and str(date_header).strip().upper() == template.date_header.strip().upper()):
                            date_format = str(df.iloc[start_row, col]).strip().replace(' ','').replace("'","")
                            if (date_format is not None and date_format is not np.nan):
                                try:
                                    date_format_datetime = datetime.strptime(date_format, template.date_format)
                                    if date_format_datetime.strftime(template.date_format) == date_format:
                                        found_date = True
                                    # CHECK DATE FORMAT TYPE "1/1/2023" (WITHOUT ZEROES)
                                    elif (len(date_format) <= 10):
                                            date1_array = date_format.replace('-','/').split('/')
                                            date2_array = date_format_datetime.strftime(template.date_format).replace('-','/').split('/')
                                            if (len(date1_array) == 3 and len(date2_array) == 3):
                                                if ( int(date1_array[0]) == int(date2_array[0]) and int(date1_array[1]) == int(date2_array[1]) and int(date1_array[2]) == int(date2_array[2]) ):
                                                    found_date = True
                                    # CHECK DATE FORMAT WHEN 'Z' IS IN TIMESTAMP
                                    elif 'Z' in date_format:
                                        date_format_datetime = date_format_datetime.strftime(template.date_format)[:-4] + "Z"
                                        if date_format_datetime == date_format:
                                            found_date = True
                                except ValueError as err:
                                    print(f"Error: {err}")
                                    pass

                    # Get Amount Header
                    if (template.amount_col is not None):
                        row = self.excel_row_number(template.header_row)
                        col = self.excel_col_number(template.amount_col)
                        if (row != None and col != None):
                            amount_header = df.iloc[row, col]
                            if (amount_header != None and amount_header != np.nan and str(amount_header).strip().upper() == template.amount_header.strip().upper()):
                                found_amount = True
                    elif (template.amount_in_col is not None and template.amount_out_col is not None):
                        row = self.excel_row_number(template.header_row)
                        col_in  = self.excel_col_number(template.amount_in_col)
                        col_out = self.excel_col_number(template.amount_out_col)
                        if (row != None and col_in != None and col_out != None):
                            amount_in_header = df.iloc[row, col_in]
                            amount_out_header = df.iloc[row, col_out]
                            if (amount_in_header != None and amount_in_header != np.nan and str(amount_in_header).strip().upper() == template.amount_in_header.strip().upper() and
                                amount_out_header != None and amount_out_header != np.nan and str(amount_out_header).strip().upper() == template.amount_out_header.strip().upper()):
                                found_amount = True

                    # Get Concept Header
                    row = self.excel_row_number(template.header_row)
                    col = self.excel_col_number(template.concept_col)
                    if (row != None and col != None):
                        concept_header = df.iloc[row, col]
                        if (concept_header != None and concept_header != np.nan and str(concept_header).strip().upper() == template.concept_header.strip().upper()):
                            found_concept = True

                    # Get Observation Header
                    row = self.excel_row_number(template.header_row)
                    col = self.excel_col_number(template.observation_col)
                    if (row != None and col != None):
                        observation_header = df.iloc[row, col]
                        if (observation_header != None and observation_header != np.nan and str(observation_header).strip().upper() == template.observation_header.strip().upper()):
                            found_observation = True

                    # print(f"Bank Template Name: {template.bank_name} | found_bank_name: {found_bank_name} found_date: {found_date} found_amount: {found_amount} found_concept: {found_concept} found_observation: {found_observation}")
                    if (found_bank_name == True or found_bank_name == None):
                        if(found_date and found_amount and found_concept and found_observation):
                            banktemplate = template
                            break
                except Exception as e:
                    pass

            if (banktemplate != None):
                print(f"Bank Template Found:  {banktemplate.bank_name}")

                # Get COL Numbers
                date_col = self.excel_col_number(template.date_col)
                amount_col = self.excel_col_number(template.amount_col)
                amount_in_col = self.excel_col_number(template.amount_in_col)
                amount_out_col = self.excel_col_number(template.amount_out_col)
                concept_col = self.excel_col_number(template.concept_col)
                observation_col = self.excel_col_number(template.observation_col)
                currency_col = self.excel_col_number(template.currency_col)
                currency_in_col = self.excel_col_number(template.currency_in_col)
                currency_out_col = self.excel_col_number(template.currency_out_col)

                # Get ROW Order and Date Format
                row_order = template.row_order
                date_format = template.date_format
                print(f"Row Order: {row_order} | Date Format: {date_format}")

                # Set Default Values
                next_mv_year = '0000'
                next_mv_month = '00'
                next_mv_number = -1
                last_mv = None
                count_mv = 0
                count_dup_mv = 0

                # Read all rows and create movements
                for index, row in df.iloc[::row_order].iterrows():
                    if ( index >= int(template.start_row) - 1 ):

                        # Skip Empty Rows
                        if (row.isnull().all()):
                            continue
                        else:
                            v1 = row.iloc[date_col] if date_col != None and not pd.isna(row.iloc[date_col]) else None
                            v2 = row.iloc[amount_col] if amount_col != None and not pd.isna(row.iloc[amount_col]) else None
                            v2a = row.iloc[amount_in_col] if amount_in_col != None and not pd.isna(row.iloc[amount_in_col]) else None
                            v2b = row.iloc[amount_out_col] if amount_out_col != None and not pd.isna(row.iloc[amount_out_col]) else None
                            v3 = row.iloc[concept_col] if concept_col != None and not pd.isna(row.iloc[concept_col]) else None
                            v4 = row.iloc[observation_col] if observation_col != None and not pd.isna(row.iloc[observation_col]) else None
                            v5 = row.iloc[currency_col] if currency_col != None and not pd.isna(row.iloc[currency_col]) else None
                            v5a = row.iloc[currency_in_col] if currency_in_col != None and not pd.isna(row.iloc[currency_in_col]) else None
                            v5b = row.iloc[currency_out_col] if currency_out_col != None and not pd.isna(row.iloc[currency_out_col]) else None
                            if (v1 == None and v2 == None and v2a == None and v2b == None and v3 == None and v4 == None and v5 == None and v5a == None and v5b == None):
                                continue

                        # Count Movements
                        count_mv += 1

                        # Get Date Value
                        date = row.iloc[date_col]
                        if isinstance(date, datetime):
                            date = date.date()
                        else:
                            # Check for NaN or invalid date values
                            date_str = str(date).strip().replace(' ','').replace("'","")
                            if date_str.lower() == 'nan' or date_str == '' or pd.isna(date):
                                print(f"Skipping row {index}: Invalid date value '{date_str}'")
                                count_mv -= 1  # Don't count this as a processed movement
                                continue
                            try:
                                date = datetime.strptime(date_str, date_format).date()
                            except ValueError as e:
                                print(f"Skipping row {index}: Date parsing error - {e}")
                                count_mv -= 1  # Don't count this as a processed movement
                                continue

                        # Get Amount Value
                        amount = None
                        currency_code_in_amount = None
                        if (amount_col != None):
                            amount = row.iloc[amount_col]
                            if isinstance(amount, (str)):
                                amount = str(amount).strip()
                                if '$' in amount:
                                    currency_code_in_amount = 'USD'
                                elif '€' in amount:
                                    currency_code_in_amount = 'EUR'
                                elif '£' in amount:
                                    currency_code_in_amount = 'GBP'
                                amount = amount.replace(' ','').replace('$','').replace('€','').replace('£','').replace('+','')

                            if amount == "-":
                                amount = 0
                            if not isinstance(amount, (float, int)):
                                if (template.decimal_separator == ','):
                                    amount = float( str(amount).replace('.', '').replace(',', '.') )
                                elif (template.decimal_separator == '.'):
                                    amount = float( str(amount).replace(',', '') )
                                else:
                                    amount = float(amount)
                        elif (amount_in_col != None and amount_out_col != None):
                            amount_in  = row.iloc[amount_in_col]
                            amount_out = row.iloc[amount_out_col]
                            if amount_in_col is not None and amount_in is not None and not pd.isna(amount_in):
                                if amount_in == "-":
                                    amount_in = 0
                                if not isinstance(amount_in, (float, int)):
                                    if (template.decimal_separator == ','):
                                        amount_in = float( str(amount_in).replace('.', '').replace(',', '.') )
                                    elif (template.decimal_separator == '.'):
                                        amount_in = float( str(amount_in).replace(',', '') )
                                    else:
                                        amount_in = float(amount_in)
                                amount = amount_in
                            elif amount_out_col is not None and amount_out is not None and not pd.isna(amount_out):
                                if amount_out == "-":
                                    amount_out = 0
                                if not isinstance(amount_out, (float, int)):
                                    if (template.decimal_separator == ','):
                                        amount_out = float( str(amount_out).replace('.', '').replace(',', '.') )
                                    elif (template.decimal_separator == '.'):
                                        amount_out = float( str(amount_out).replace(',', '') )
                                    else:
                                        amount_out = float(amount_out)
                                if (amount_out > 0):
                                    amount_out = amount_out * -1
                                amount = amount_out

                        # Change the sign of the amount if the template is backward
                        if template.backward_amount is True:
                            amount = amount * -1

                        # Get Currency Value
                        currency = None
                        if (currency_col != None):
                            currency_code = row.iloc[currency_col]
                        elif (currency_in_col != None and currency_out_col != None):
                            currency_in_code = row.iloc[currency_in_col]
                            currency_out_code = row.iloc[currency_out_col]
                            if currency_in_col is not None and currency_in_code is not None and not pd.isna(currency_in_code):
                                currency_code = currency_in_code
                            elif currency_out_col is not None and currency_out_code is not None and not pd.isna(currency_out_code):
                                currency_code = currency_out_code
                            else:
                                currency_code = None
                        elif (currency_code_in_amount != None):
                            currency_code = currency_code_in_amount
                        else:
                            currency_code = None

                        if (currency_code != None):
                            if (template.multi_currency == True):
                                currency = Currency.objects.filter(code = currency_code).first()
                            elif (bank.bank_currency != None and bank.bank_currency.code == currency_code):
                                currency = bank.bank_currency
                            elif (bank.bank_currency != None and bank.bank_currency.code != currency_code):
                                raise ValueError(f"Currency Code {currency_code} does not match with Bank Currency {currency.code}")
                            else:
                                raise ValueError(f"Currency Code {currency_code} does not match with Bank Currency {currency.code}")
                        else:
                            currency = bank.bank_currency

                        print(f"bank.bank_currency: {bank.bank_currency} | currency_code: {currency_code} | currency: {currency}")

                        # Convert Amount to Euros
                        amount_euros = amount
                        if (currency != None and amount != None and date != None and currency != None and currency.code != 'EUR'):
                            amount_euros = self.convertCurrencyToEur(currency.code, amount, date.isoformat())

                        # Get Concept Value
                        concept = row.iloc[concept_col]
                        if (concept == np.nan or str(concept).lower() == 'nan'):
                            concept = ''

                        # Get Observation Value
                        observation = row.iloc[observation_col]
                        if (observation == np.nan or str(observation).lower() == 'nan' or observation == concept):
                            observation = ''

                        print(f"Row: {index} | Date: {date.isoformat()} Amount: {amount} {currency.code} Concept: {concept} Observation: {observation}")

                        # Get Next Movement Number If Month/Year Changes
                        if (next_mv_year != str(date.year).zfill(4) or next_mv_month != str(date.month).zfill(2) ):
                            next_mv_year  = str(date.year).zfill(4)
                            next_mv_month = str(date.month).zfill(2)
                            next_mv_number = 1

                            regex = r'^\d{4}-\d{2}-\d{6}$'
                            mv_start = f'{next_mv_year}-{next_mv_month}-'
                            last_mv = Movement.objects.filter(bank = bank, movement_number__regex=regex , movement_number__startswith=mv_start).order_by('-movement_number').first()
                            if (last_mv != None and last_mv.movement_number != None):
                                next_mv_number = int(str(last_mv.movement_number)[8:14]) + 1

                        # If not exists Movements in this year/month => Create Movement
                        # If exists Movements in this year/month => Check if Movement already exists (Discard Duplicates)
                        if (last_mv == None or date > last_mv.movement_date):
                            # Create Movement
                            movement = Movement()
                            movement.bank = bank
                            movement.movement_date = date.isoformat()
                            movement.amount = amount
                            movement.amount_euros = amount_euros
                            movement.currency = currency
                            movement.concept = concept
                            movement.observation = observation
                            movement.status = mv_status_pending
                            if next_mv_number > 0:
                                movement.movement_number = str(next_mv_year).zfill(4) + '-' + str(next_mv_month).zfill(2) + '-' + str(next_mv_number).zfill(6)
                                next_mv_number += 1
                            count_new_mv += 1
                            movement.save()
                            # print(f"Movement Created A: {movement.amount} {movement.currency.code}  ({movement.amount_euros}€)")
                        else:
                            # Build a query that will match existing movements with same key attributes
                            duplicate_query = Movement.objects.filter(
                                bank=bank,
                                movement_date=date.isoformat(),
                                amount=amount
                            )

                            # Add concept and observation filters for more precise matching
                            if concept and concept.strip():
                                # Check if there's a movement with the same concept
                                concept_match = duplicate_query.filter(concept=concept)
                                if concept_match.exists():
                                    count_dup_mv += 1
                                    print(f"Movement with identical concept already exists: {date.isoformat()} {amount} {concept}")
                                    continue

                            if observation and observation.strip():
                                # Check if there's a movement with the same observation
                                observation_match = duplicate_query.filter(observation=observation)
                                if observation_match.exists():
                                    count_dup_mv += 1
                                    print(f"Movement with identical observation already exists: {date.isoformat()} {amount} {observation}")
                                    continue

                            # If no duplicates found, create the movement
                            movement = Movement()
                            movement.bank = bank
                            movement.movement_date = date.isoformat()
                            movement.amount = amount
                            movement.amount_euros = amount_euros
                            movement.currency = currency
                            movement.concept = concept
                            movement.observation = observation
                            movement.status = mv_status_pending
                            if next_mv_number > 0:
                                movement.movement_number = str(next_mv_year).zfill(4) + '-' + str(next_mv_month).zfill(2) + '-' + str(next_mv_number).zfill(6)
                                next_mv_number += 1
                            count_new_mv += 1
                            movement.save()
                            print(f"Movement Created: {movement.pk} {movement.movement_date} {movement.amount} {movement.currency.code} {movement.concept}")

                # Set Error: True/False and Message
                if (count_mv > 0 and count_new_mv > 0 and count_mv == count_new_mv):
                    error = False
                    errormsg = f"Se procesó el archivo correctamente. Se han insertado {count_new_mv} movimientos."
                elif (count_mv > 0 and count_new_mv > 0 and count_mv > count_new_mv):
                    error = False
                    errormsg = f"Se procesó el archivo con descartes. Se han insertado {count_new_mv} movimientos de un toal de {count_mv}."
                elif (count_mv > 0 and count_new_mv == 0 and count_dup_mv > 0):
                    error = True
                    errormsg = "No se ha insertado ningún movimiento porque estan duplicados (ya existian). Se han descartado todos."
                elif (count_mv > 0 and count_new_mv == 0):
                    error = True
                    errormsg = "No se ha insertado ningún movimiento. Se han descartado todos."
                elif (count_mv == 0):
                    error = True
                    errormsg = "No se ha insertado ningún movimiento. El archivo no contiene movimientos."
                else:
                    error = True
                    errormsg = "Error desconocido procesando el archivo."
            else:
                error = True
                errormsg = "No se encontró un banco que coincida con este archivo."
        else:
            error = True
            errormsg = "El formulario no es válido."

        print(f"DEBUG: process_movement_excel returning count_new_mv={count_new_mv}")

        return {'error': error, 'message': errormsg, 'banktemplate': banktemplate, 'count_new_mv': count_new_mv}

class MovementsManualCreateView(LoginRequiredMixin, IsSellerShortnamePermission, CreateView):
    model = BankMovementsImporter
    form_class = BankMovementsChangeForm
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_form_kwargs(self):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        bank = Bank.objects.filter(bank_seller = seller, id = self.kwargs["pk"]).first()
        pendingstatus = BankMovementImporterStatus.objects.filter(code = "pending").first()
        kwargs = super().get_form_kwargs()
        kwargs["data"] = kwargs["data"].copy()
        kwargs["data"]["seller"] = seller
        kwargs["data"]["bank"] = bank
        kwargs["data"]["status"] = pendingstatus
        kwargs["data"]["error_message"] = "Pendiente de procesar"
        return kwargs

    def form_valid(self, form):
        self.object = form.save()

        if True:
        # try:
            resp = self.process_movement_excel(form)
            error = resp['error']
            msg = resp['message']
            template = BankMovementTemplate.objects.filter(code="0017").first()
            # Get count_new_mv from the response
            count_new_mv = resp.get('count_new_mv', 0)

            if (error == False):
                # Set 'status' to 'processed' and 'error_message'
                processed_status = BankMovementImporterStatus.objects.filter(code="processed").first()
                self.object.status = processed_status
                self.object.error_message = msg if msg != None and len(msg) > 0 else "Procesado Correctamente"
                self.object.template = template

                # Auto-apply bank rules for this bank after successful import
                if count_new_mv > 0:  # Only if new movements were created
                    print(f"DEBUG - form_valid: Applying rules for {count_new_mv} new movements")
                    self.rule_result = self.apply_bank_rules_to_imported_movements()
                    print(f"DEBUG - form_valid: Rule application result: {self.rule_result}")
                else:
                    print(f"DEBUG - form_valid: No new movements to apply rules to")

                redirect = self.get_success_url()
            else:
                # Set 'status' to 'error' and 'error_message'
                error_status = BankMovementImporterStatus.objects.filter(code="error").first()
                self.object.status = error_status
                self.object.error_message = msg if msg != None and len(msg) > 0 else "Error procesando el archivo"
                self.object.template = template
                redirect = self.get_unsuccess_url()
        # except Exception as e:
        #     print(f"Error procesando el archivo: {e}")
        #     # Set 'status' to 'error' and 'error_message'
        #     self.object.status = BankMovementImporterStatus.objects.filter(code="error").first()
        #     self.object.error_message = "Error procesando el archivo"
        #     redirect = self.get_unsuccess_url()

        # Save Object
        self.object.save()

        # Redirect to success or unsuccess url
        return HttpResponseRedirect(redirect)

    def apply_bank_rules_to_imported_movements(self):
        """Apply bank rules to newly imported movements."""
        try:
            seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
            bank = Bank.objects.filter(bank_seller=seller, id=self.kwargs["pk"]).first()

            print(f"DEBUG - apply_bank_rules: Seller={seller.shortname}, Bank ID={bank.id if bank else None}")

            if not bank:
                print(f"Error: Bank with ID {self.kwargs['pk']} not found")
                return None

            # Get rules for this bank, ordered by priority (highest first)
            rules = BankRule.objects.filter(
                bank=bank,
                seller=seller,
                is_active=True
            ).order_by('-priority')

            print(f"DEBUG - apply_bank_rules: Found {rules.count()} rules for bank ID {bank.id}")

            if not rules.exists():
                print(f"No rules found for bank ID {bank.id}")
                return None

            # Get pending and partially conciliated movements for this bank
            movements = Movement.objects.filter(
                bank=bank,
                status_id__in=['pending', 'partially-conciliated']
            )

            total_movements = movements.count()
            print(f"DEBUG - apply_bank_rules: Found {total_movements} movements to process")

            matched_movements = 0

            # Process each movement
            for movement in movements:
                # Skip if movement is already fully conciliated
                if movement.status_id == 'conciliated':
                    continue

                print(f"DEBUG - apply_bank_rules: Processing movement ID={movement.id}, concept={movement.concept}")

                # Process through rules in priority order
                for rule in rules:
                    # Skip rules without an accounting account
                    if not rule.accounting_account:
                        continue

                    # Get movement concept
                    concept = movement.concept or ""

                    # Case-insensitive comparison
                    concept_lower = concept.lower()
                    pattern_lower = rule.pattern.lower()

                    # Apply rule based on match type
                    match = False
                    match_type = rule.rule_match_type
                    if match_type == 'equals':
                        match = concept_lower == pattern_lower
                    elif match_type == 'starts_with':
                        match = concept_lower.startswith(pattern_lower)
                    elif match_type == 'ends_with':
                        match = concept_lower.endswith(pattern_lower)
                    elif match_type == 'contains':
                        match = pattern_lower in concept_lower

                    print(f"DEBUG - apply_bank_rules: Rule ID={rule.id}, pattern={rule.pattern}, match_type={match_type}, match={match}")

                    # If match found, create reconciliation
                    if match:
                        # Skip if movement is already fully conciliated
                        if movement.status_id == 'conciliated':
                            break

                        print(f"DEBUG - apply_bank_rules: Match found! Creating reconciliation")

                        # Get reconciliation type
                        rec_type = ReconciliationType.objects.filter(pk='account').first()
                        if not rec_type:
                            continue

                        # Calculate pending amount
                        reconciliations_amount = Reconciliation.objects.filter(movement=movement).aggregate(total=Sum(F('amount')))['total'] or 0

                        # Check for existing reconciliation with same account
                        existing_reconciliation = Reconciliation.objects.filter(
                            movement=movement,
                            accounting_account=rule.accounting_account
                        ).first()

                        # Exclude existing reconciliation from calculation if it exists
                        if existing_reconciliation:
                            reconciliations_amount -= existing_reconciliation.amount

                        # Calculate pending amount based on movement type
                        if movement.amount_euros < 0:
                            if reconciliations_amount < 0:
                                pending_amount = float(movement.amount_euros) - float(reconciliations_amount)
                            else:
                                pending_amount = float(movement.amount_euros) + float(reconciliations_amount)
                        else:
                            if reconciliations_amount < 0:
                                pending_amount = float(movement.amount_euros) + float(reconciliations_amount)
                            else:
                                pending_amount = float(movement.amount_euros) - float(reconciliations_amount)

                        # Round to 2 decimals
                        pending_amount = round(pending_amount, 2)

                        # For account reconciliation, use opposite sign
                        amount = -pending_amount

                        try:
                            # If existing reconciliation exists, delete it
                            if existing_reconciliation:
                                existing_reconciliation.delete()

                                # Recalculate pending amount
                                reconciliations_amount = Reconciliation.objects.filter(movement=movement).aggregate(total=Sum(F('amount')))['total'] or 0

                                if movement.amount_euros < 0:
                                    if reconciliations_amount < 0:
                                        pending_amount = float(movement.amount_euros) - float(reconciliations_amount)
                                    else:
                                        pending_amount = float(movement.amount_euros) + float(reconciliations_amount)
                                else:
                                    if reconciliations_amount < 0:
                                        pending_amount = float(movement.amount_euros) + float(reconciliations_amount)
                                    else:
                                        pending_amount = float(movement.amount_euros) - float(reconciliations_amount)

                                pending_amount = round(pending_amount, 2)
                                amount = -pending_amount

                            # Create new reconciliation
                            reconciliation = Reconciliation(
                                movement=movement,
                                accounting_account=rule.accounting_account,
                                accounting_account_detail=rule.accounting_account.code,
                                amount=amount,
                                type=rec_type,
                                invoice=None,
                                bank=None,
                                movement_transfer=None,
                                used_in_entry=False,
                                created_at=timezone.now(),
                                modified_at=timezone.now()
                            )
                            reconciliation.save()
                            matched_movements += 1

                            # Update movement status
                            try:
                                update_movement_status(seller, movement.pk)
                            except Exception as e:
                                print(f"Error updating movement status: {e}")
                        except Exception as e:
                            print(f"Error creating reconciliation for movement {movement.pk}: {e}")

                        # We've matched this movement, move to the next
                        break

            print(f"Auto-applied rules to {matched_movements} of {total_movements} movements")
            return {
                'total_movements': total_movements,
                'matched_movements': matched_movements
            }
        except Exception as e:
            print(f"Error applying bank rules automatically: {e}")
            import traceback
            traceback.print_exc()  # This will print the full traceback
            return None

    def form_invalid(self, form):
        shortname=self.kwargs["shortname"]
        bankid=self.kwargs["pk"]
        error_message = form.errors
        if 'file' in form.errors:
            error_message = form.errors['file']
        return redirect(reverse('app_banks:bank_movements_upload', args=[shortname, bankid]) + f'?toast_message={error_message}&toast_type=error')

    def get_success_url(self) -> str:
        shortname=self.kwargs["shortname"]
        base_url = reverse('app_banks:bank_list', args=[shortname])
        # Add success message as query parameter
        if hasattr(self, 'object') and self.object:
            # Only show success message if processing was successful
            success_message = "Se procesó el archivo correctamente."

            # Add rule application info if available
            if hasattr(self, 'rule_result') and self.rule_result:
                total = self.rule_result.get('total_movements', 0)
                matched = self.rule_result.get('matched_movements', 0)
                if matched > 0:
                    success_message += f" Se aplicaron reglas a {matched} de {total} movimientos importados."

            return f"{base_url}?toast_message={success_message}&toast_type=success"
        return base_url

    def get_unsuccess_url(self) -> str:
        shortname = shortname=self.kwargs["shortname"]
        bank_id = self.kwargs["pk"]
        base_url = reverse(
            "app_banks:bank_movements_upload",
            args=[shortname, bank_id],
        )
        # Add error message as query parameter if it exists
        if hasattr(self, 'object') and self.object and self.object.error_message:
            return f"{base_url}?toast_message={self.object.error_message}&toast_type=error"
        return base_url

    def handle_no_permission(self):
            return HttpResponseRedirect(reverse("home"))

    def convertCurrencyToEur(self, currency, amount, date):
        try:
            seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        except:
            seller = None
        if seller:
            if seller.api_usage is None or seller.api_usage == "":
                api_usage = {}
            else:
                api_usage = json.loads(seller.api_usage)
            api="currency-api"
            if api in api_usage:
                api_usage[str(api)]=api_usage[str(api)]+1
                print("ya se encuentra, se actualiza, valor: " + str(api_usage[str(api)]) )
            else:
                api_usage[str(api)]=1
                print("no se encuentra, lo creamos")
            seller.api_usage = json.dumps(api_usage)
            seller.save()

        baseUrl = 'https://api.currencyapi.com/v3/'
        apikey = settings.CURRENCY_API_KEY
        taxConversion = 0
        if  currency is not None and currency == 'EUR':
            taxConversion = 1
        elif date  is not None and currency is not None :

            try:
                url = baseUrl
                currentDate = datetime.now()
                invoiceDate = datetime.strptime(date + 'T23:59:59', '%Y-%m-%dT%H:%M:%S')

                if invoiceDate < currentDate:
                    url += 'historical'
                    url += '?currencies=EUR'
                    url += '&date=' + str(date)
                else:
                    url += 'latest'
                    url += '?currencies=EUR'
                url += '&base_currency=' + currency
                url += '&apikey=' + apikey

                r = requests.get(url)
                if r is not None and r.status_code == 200:
                    data = r.json()['data']
                    print(data)
                    taxConversion = float(data['EUR']['value'])
                print('taxConversion:'+str(taxConversion))
            except Exception as error:
                print('Error on getTaxConversion: ', repr(error))

        amount = float(str(amount))
        result = float(taxConversion * amount)
        result = float( int(result*100) / 100 ) # TRUNCATE TO 2 DECIMALS
        return result

    def excel_col_number(self, letra):
        """
        Convierte una letra de A a Z en su equivalente numérico (A=0, B=1, ..., Z=25).
        """
        r = None
        if (letra != None and letra.strip() != "" and letra.isalpha()):
            letras = string.ascii_uppercase
            r = letras.index(letra.upper())
        return r

    def excel_row_number(self, fila):
        """
        Resta 1 a la fila, asumiendo que las filas comienzan desde 1.
        """
        r = None
        if (fila != None and fila.strip() != "" and fila.isnumeric() and int(fila) > 0):
            r = int(fila) - 1
        return r

    def process_movement_excel(self, form):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        bank = Bank.objects.filter(bank_seller = seller, id = self.kwargs["pk"]).first()
        mv_status_pending = MovementStatus.objects.filter(code = "pending").first()

        error = None
        errormsg= None
        banktemplate = None
        count_new_mv = 0

        if form.is_valid():
            excel_file = self.request.FILES["file"]

            # Read Excel File
            df = None
            if excel_file.name.endswith('.xls'):
                # Reiniciar el puntero del archivo al principio
                excel_file.seek(0)
                # Leer el archivo Excel con Pandas
                df = pd.read_excel(excel_file, engine="xlrd", header=None)
            elif excel_file.name.endswith('.xlsx'):
                try:
                    # Reiniciar el puntero del archivo al principio
                    excel_file.seek(0)
                    # Leer el archivo Excel con Pandas
                    df = pd.read_excel(excel_file, engine="openpyxl", header=None)
                except:
                    # Reiniciar el puntero del archivo al principio
                    excel_file.seek(0)
                    # Leer el archivo Excel con pylightxl
                    db = pylightxl.readxl(excel_file)
                    # data sheet
                    name_first_sheet = db.ws_names[0]
                    sheet_data = list(db.ws(ws=name_first_sheet).rows)
                    # create dataframe
                    df = pd.DataFrame(data=sheet_data[0:], columns=sheet_data[0])
            print(f"df: {df}")


            date_col = None
            amount_col = None
            amount_in_col = None
            amount_out_col = None
            concept_col = None
            observation_col = None
            currency_col = None
            currency_in_col = None
            currency_out_col = None
            row_order = None
            date_format = None
            start_row = None
            end_row = None
            decimal_separator = None
            backward_amount = None

            # Get COL Numbers
            if self.request.POST.get('date_column') != "-":
                date_col = self.excel_col_number(self.request.POST.get('date_column'))
            if self.request.POST.get('amount_column') != "-":
                amount_col = self.excel_col_number(self.request.POST.get('amount_column'))
            if self.request.POST.get('amount_in_column') != "-":
                amount_in_col = self.excel_col_number(self.request.POST.get('amount_in_column'))

            if self.request.POST.get('amount_out_column') != "-":
                amount_out_col = self.excel_col_number(self.request.POST.get('amount_out_column'))
            if self.request.POST.get('concept_column') != "-":
                concept_col = self.excel_col_number(self.request.POST.get('concept_column'))

            if self.request.POST.get('observations_column') != "-":
                observation_col = self.excel_col_number(self.request.POST.get('observations_column'))
            if self.request.POST.get('currency_column') != "-":
                currency_col = self.excel_col_number(self.request.POST.get('currency_column'))
            if self.request.POST.get('currency_in_column') != "-":
                currency_in_col = self.excel_col_number(self.request.POST.get('currency_in_column'))
            if self.request.POST.get('currency_out_column') != "-":
                currency_out_col = self.excel_col_number(self.request.POST.get('currency_out_column'))

            # Get ROW Order and Date Format
            row_order = int(self.request.POST.get('order'))
            if self.request.POST.get('date_format') != "-":
                date_format = self.request.POST.get('date_format')
            start_row = self.excel_row_number(self.request.POST.get('start_row'))+1
            end_row = self.excel_row_number(self.request.POST.get('end_row'))
            if self.request.POST.get('decimal_separator') != "-":
                decimal_separator = self.request.POST.get('decimal_separator')

            # Get if the sign of the amount is backward
            if self.request.POST.get('backward_amount') != "-":
                backward_amount =  True if self.request.POST.get('backward_amount') == 'true' else False


            # Set Default Values
            next_mv_year = '0000'
            next_mv_month = '00'
            next_mv_number = -1
            last_mv = None
            count_mv = 0
            count_dup_mv = 0

            # Read all rows and create movements
            for index, row in df.iloc[::row_order].iterrows():
                if ( index >= int(start_row) - 1  and index <= int(end_row)):

                    # Check if row has essential data (skip empty rows)
                    date_value = row.iloc[date_col] if date_col != None and not pd.isna(row.iloc[date_col]) else None
                    amount_value = row.iloc[amount_col] if amount_col != None and not pd.isna(row.iloc[amount_col]) else None
                    amount_in_value = row.iloc[amount_in_col] if amount_in_col != None and not pd.isna(row.iloc[amount_in_col]) else None
                    amount_out_value = row.iloc[amount_out_col] if amount_out_col != None and not pd.isna(row.iloc[amount_out_col]) else None

                    # Skip row if essential fields are empty
                    if (date_value == None and amount_value == None and amount_in_value == None and amount_out_value == None):
                        continue

                    # Skip row if date is missing (date is required)
                    if date_value == None:
                        print(f"Skipping row {index}: Missing date value")
                        continue

                    # Count Movements
                    count_mv += 1

                    # Get Date Value
                    date = row.iloc[date_col]
                    if isinstance(date, datetime):
                        date = date.date()
                    else:
                        # Check for NaN or invalid date values
                        date_str = str(date).strip()
                        if date_str.lower() == 'nan' or date_str == '' or pd.isna(date):
                            print(f"Skipping row {index}: Invalid date value '{date_str}'")
                            count_mv -= 1  # Don't count this as a processed movement
                            continue
                        try:
                            date = datetime.strptime(date_str, date_format).date()
                        except ValueError as e:
                            print(f"Skipping row {index}: Date parsing error - {e}")
                            count_mv -= 1  # Don't count this as a processed movement
                            continue

                    # Get Amount Value
                    amount = None
                    currency_code_in_amount = None
                    if (amount_col != None):
                        amount = row.iloc[amount_col]
                        if isinstance(amount, (str)):
                            amount = str(amount).strip()
                            if '$' in amount:
                                currency_code_in_amount = 'USD'
                            elif '€' in amount:
                                currency_code_in_amount = 'EUR'
                            elif '£' in amount:
                                currency_code_in_amount = 'GBP'
                            amount = amount.replace(' ','').replace('$','').replace('€','').replace('£','').replace('+','')

                        if not isinstance(amount, (float, int)):
                            if (decimal_separator == ','):
                                amount = float( str(amount).replace('.', '').replace(',', '.') )
                            elif (decimal_separator == '.'):
                                amount = float( str(amount).replace(',', '') )
                            else:
                                amount = float(amount)
                    elif (amount_in_col != None and amount_out_col != None):
                        amount_in  = row.iloc[amount_in_col]
                        amount_out = row.iloc[amount_out_col]
                        if amount_in_col is not None and amount_in is not None and not pd.isna(amount_in):
                            if not isinstance(amount_in, (float, int)):
                                if (decimal_separator == ','):
                                    amount_in = float( str(amount_in).replace('.', '').replace(',', '.') )
                                elif (decimal_separator == '.'):
                                    amount_in = float( str(amount_in).replace(',', '') )
                                else:
                                    amount_in = float(amount_in)
                            amount = amount_in
                        elif amount_out_col is not None and amount_out is not None and not pd.isna(amount_out):
                            if not isinstance(amount_out, (float, int)):
                                if (decimal_separator == ','):
                                    amount_out = float( str(amount_out).replace('.', '').replace(',', '.') )
                                elif (decimal_separator == '.'):
                                    amount_out = float( str(amount_out).replace(',', '') )
                                else:
                                    amount_out = float(amount_out)
                            if (amount_out > 0):
                                amount_out = amount_out * -1
                            amount = amount_out

                    # Change the sign of the amount if the template is backward
                    if backward_amount is not None and backward_amount is True:
                        amount = amount * -1

                    # Get Currency Value
                    currency = None
                    if (currency_col != None):
                        currency_code = row.iloc[currency_col]
                    elif (currency_in_col != None and currency_out_col != None):
                        currency_in_code = row.iloc[currency_in_col]
                        currency_out_code = row.iloc[currency_out_col]
                        if currency_in_col is not None and currency_in_code is not None and not pd.isna(currency_in_code):
                            currency_code = currency_in_code
                        elif currency_out_col is not None and currency_out_code is not None and not pd.isna(currency_out_code):
                            currency_code = currency_out_code
                        else:
                            currency_code = None
                    elif (currency_code_in_amount != None):
                        currency_code = currency_code_in_amount
                    else:
                        currency_code = None

                    if (currency_code != None):
                        currency = Currency.objects.filter(code = currency_code).first()
                    else:
                        currency = bank.bank_currency

                    print(f"bank.bank_currency: {bank.bank_currency} | currency_code: {currency_code} | currency: {currency}")

                    # Convert Amount to Euros
                    amount_euros = amount
                    if (currency != None and amount != None and date != None and currency != None and currency.code != 'EUR'):
                        amount_euros = self.convertCurrencyToEur(currency.code, amount, date.isoformat())

                    # Get Concept Value
                    concept = ''
                    if (concept_col != None):
                        concept = row.iloc[concept_col]
                        if (concept == np.nan or str(concept).lower() == 'nan'):
                            concept = ''

                    # Get Observation Value
                    observation = ''
                    if (observation_col != None):
                        observation = row.iloc[observation_col]
                        if (observation == np.nan or str(observation).lower() == 'nan' or observation == concept):
                            observation = ''

                    print(f"Row: {index} | Date: {date.isoformat()} Amount: {amount} {currency.code} Concept: {concept} Observation: {observation}")

                    # Get Next Movement Number If Month/Year Changes
                    if (next_mv_year != str(date.year).zfill(4) or next_mv_month != str(date.month).zfill(2) ):
                        next_mv_year  = str(date.year).zfill(4)
                        next_mv_month = str(date.month).zfill(2)
                        next_mv_number = 1

                        regex = r'^\d{4}-\d{2}-\d{6}$'
                        mv_start = f'{next_mv_year}-{next_mv_month}-'
                        last_mv = Movement.objects.filter(bank = bank, movement_number__regex=regex , movement_number__startswith=mv_start).order_by('-movement_number').first()
                        if (last_mv != None and last_mv.movement_number != None):
                            next_mv_number = int(str(last_mv.movement_number)[8:14]) + 1

                    # If not exists Movements in this year/month => Create Movement
                    # If exists Movements in this year/month => Check if Movement already exists (Discard Duplicates)
                    if (last_mv == None or date > last_mv.movement_date):
                        # Create Movement
                        movement = Movement()
                        movement.bank = bank
                        movement.movement_date = date.isoformat()
                        movement.amount = amount
                        movement.amount_euros = amount_euros
                        movement.currency = currency
                        movement.concept = concept
                        movement.observation = observation
                        movement.status = mv_status_pending
                        if next_mv_number > 0:
                            movement.movement_number = str(next_mv_year).zfill(4) + '-' + str(next_mv_month).zfill(2) + '-' + str(next_mv_number).zfill(6)
                            next_mv_number += 1
                        count_new_mv += 1
                        movement.save()
                        # print(f"Movement Created A: {movement.amount} {movement.currency.code}  ({movement.amount_euros}€)")
                    else:
                        qty_mv = Movement.objects.filter(bank=bank, movement_date=date.isoformat(), amount=amount, observation=observation).count()
                        if (qty_mv == 0):
                            # Create Movement
                            movement = Movement()
                            movement.bank = bank
                            movement.movement_date = date.isoformat()
                            movement.amount = amount
                            movement.amount_euros = amount_euros
                            movement.currency = currency
                            movement.concept = concept
                            movement.observation = observation
                            movement.status = mv_status_pending
                            if next_mv_number > 0:
                                movement.movement_number = str(next_mv_year).zfill(4) + '-' + str(next_mv_month).zfill(2) + '-' + str(next_mv_number).zfill(6)
                                next_mv_number += 1
                            count_new_mv += 1
                            movement.save()
                            # print(f"Movement Created B: {movement.amount} {movement.currency.code}  ({movement.amount_euros}€)")
                        else:
                            count_dup_mv += 1
                            print(f"Movement already exists: {date.isoformat()} {amount} {concept} {observation}")


            # Set Error: True/False and Message
            if (count_mv > 0 and count_new_mv > 0 and count_mv == count_new_mv):
                error = False
                errormsg = f"Se procesó el archivo correctamente. Se han insertado {count_new_mv} movimientos."
            elif (count_mv > 0 and count_new_mv > 0 and count_mv > count_new_mv):
                error = False
                errormsg = f"Se procesó el archivo con descartes. Se han insertado {count_new_mv} movimientos de un toal de {count_mv}."
            elif (count_mv > 0 and count_new_mv == 0 and count_dup_mv > 0):
                error = True
                errormsg = "No se ha insertado ningún movimiento porque estan duplicados (ya existian). Se han descartado todos."
            elif (count_mv > 0 and count_new_mv == 0):
                error = True
                errormsg = "No se ha insertado ningún movimiento. Se han descartado todos."
            elif (count_mv == 0):
                error = True
                errormsg = "No se ha insertado ningún movimiento. El archivo no contiene movimientos."
            else:
                error = True
                errormsg = "Error desconocido procesando el archivo."

        else:
            error = True
            errormsg = "El formulario no es válido."

        print(f"DEBUG: process_movement_excel returning count_new_mv={count_new_mv}")

        return {'error': error, 'message': errormsg, 'banktemplate': banktemplate, 'count_new_mv': count_new_mv}